<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "0710"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A340BDFEBCA49239A941883D"
               BuildableName = "Prekliatededicstvo.app"
               BlueprintName = "Prekliatededicstvo"
               ReferencedContainer = "container:Prekliatededicstvo.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
      </Testables>
      <MacroExpansion>
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A340BDFEBCA49239A941883D"
               BuildableName = "Prekliatededicstvo.app"
               BlueprintName = "Prekliatededicstvo"
               ReferencedContainer = "container:Prekliatededicstvo.xcodeproj">
            </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A340BDFEBCA49239A941883D"
               BuildableName = "Prekliatededicstvo.app"
               BlueprintName = "Prekliatededicstvo"
               ReferencedContainer = "container:Prekliatededicstvo.xcodeproj">
            </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
      <AdditionalOptions>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Debug"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A340BDFEBCA49239A941883D"
               BuildableName = "Prekliatededicstvo.app"
               BlueprintName = "Prekliatededicstvo"
               ReferencedContainer = "container:Prekliatededicstvo.xcodeproj">
            </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Debug"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>

