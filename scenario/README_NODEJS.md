# 🎨 Scenario API Node.js Generator

Funkčný Node.js skript pre generovanie obr<PERSON><PERSON><PERSON> pomocou Scenario API s vaš<PERSON>i kľú<PERSON>.

## 🚀 Rýchle Spustenie

### 1. Inštal<PERSON>cia závislostí
```bash
cd scenario
npm install
```

### 2. Konfigurácia
API kľúče sú už nakonfigurované v `.env` súbore:
```env
SCENARIO_API_KEY=api_ttVVbj1gnKRqUbrZytSiVgf4
SCENARIO_API_SECRET=api_BcbAFAXVD1HcedCEKw7hmhLW
```

### 3. Spustenie
```bash
# Základné generovanie s predvoleným promptom
npm start

# Vlastný prompt
node generate-image.js "Van Helsing vampire hunter with crossbow"

# Test API pripojenia
npm test
```

## 📋 Dostupné Skripty

### `generate-image.js` - <PERSON>lavný generátor
```bash
# Predvolený prompt
node generate-image.js

# Vlastný prompt
node generate-image.js "Gothic castle at night"

# Test viacerých promptov
node generate-image.js --test
```

### `test-api.js` - Test API pripojenia
```bash
node test-api.js
```

## ⚙️ Konfigurácia

### Premenné v `.env`:
- `SCENARIO_API_KEY` - Váš API kľúč
- `SCENARIO_API_SECRET` - Váš API secret
- `SCENARIO_BASE_URL` - Base URL (predvolene: https://api.cloud.scenario.com/v1)
- `OUTPUT_DIR` - Priečinok pre uloženie obrázkov (predvolene: ./generated-images)
- `DEFAULT_WIDTH` - Predvolená šírka (predvolene: 512)
- `DEFAULT_HEIGHT` - Predvolená výška (predvolene: 512)

### Parametre generovania:
```javascript
{
    width: 512,           // Šírka obrázka
    height: 512,          // Výška obrázka
    numImages: 1,         // Počet obrázkov
    guidanceScale: 7.5,   // Presnosť promptu
    numInferenceSteps: 30, // Kvalita (viac = lepšie, ale pomalšie)
    seed: -1              // Seed pre reprodukovateľnosť (-1 = náhodný)
}
```

## 🎯 Príklady Použitia

### Základné generovanie
```javascript
import { generateImage } from './generate-image.js';

await generateImage('Gothic haunted castle at night');
```

### S vlastnými nastaveniami
```javascript
await generateImage('Van Helsing portrait', {
    width: 768,
    height: 1024,
    numImages: 2,
    guidanceScale: 8.0
});
```

### Van Helsing témy
```bash
node generate-image.js "Van Helsing vampire hunter, Victorian era, crossbow"
node generate-image.js "Gothic vampire castle, dark atmosphere, moonlight"
node generate-image.js "Ancient crypt with stone coffins, candlelight"
node generate-image.js "Dark forest path, mysterious fog, lantern light"
```

## 📁 Výstup

Vygenerované obrázky sa ukladajú do priečinka `generated-images/` s názvami:
```
Gothic_haunted_castle_at_night_2024-01-15T10-30-45-123Z_1.png
Van_Helsing_vampire_hunter_2024-01-15T10-32-10-456Z_1.png
```

## 🔧 Riešenie Problémov

### Chyba "Invalid APIKey"
- Skontrolujte, či sú API kľúče správne nastavené v `.env`
- Overte, že kľúče neobsahujú medzery alebo špeciálne znaky

### Chyba "Unauthorized"
- Možno je potrebné iný formát autentifikácie
- Skúste spustiť `npm test` pre diagnostiku

### Chyba sťahovania
- Skontrolujte internetové pripojenie
- Overte, že priečinok `generated-images` má správne oprávnenia

## 📊 Výstup Skriptu

```
🚀 Scenario API Image Generator
================================

🔑 API Key: api_ttVVbj...
🌐 Base URL: https://api.cloud.scenario.com/v1
📁 Output Dir: ./generated-images

🎨 Spúšťam generovanie obrázka...
📝 Prompt: Gothic haunted castle at night
⚙️ Nastavenia: { width: 512, height: 512, numImages: 1 }
📡 Odosielam požiadavku na Scenario API...
📊 Status kód: 200
✅ Úspešná odpoveď z API:
{
  "images": [
    {
      "url": "https://...",
      "id": "..."
    }
  ]
}
📥 Sťahujem 1 obrázok(ov)...
🖼️ Sťahujem obrázok 1: https://...
💾 Obrázok uložený: ./generated-images/Gothic_haunted_castle_at_night_2024-01-15T10-30-45-123Z_1.png
```

## 🎮 Integrácia s Godot

Vygenerované obrázky môžete skopírovať do Godot projektu:
```bash
cp generated-images/*.png ../assets/scenario_generated/
```

---

**Tento skript poskytuje kompletné riešenie pre generovanie AI assetov pomocou Scenario API pre váš Van Helsing projekt!** 🎮✨
