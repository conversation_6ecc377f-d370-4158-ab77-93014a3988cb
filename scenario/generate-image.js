#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

// Načítanie premenných z .env súboru
dotenv.config();

// Konfigurácia API
const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = process.env.SCENARIO_BASE_URL || 'https://api.cloud.scenario.com/v1';
const OUTPUT_DIR = process.env.OUTPUT_DIR || './generated-images';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Hlavná funkcia pre generovanie obr<PERSON><PERSON>ka
async function generateImage(prompt, options = {}) {
    console.log('🎨 Spúšťam generovanie obr<PERSON>zka...');
    console.log('📝 Prompt:', prompt);
    
    // Predvolené nastavenia
    const defaultOptions = {
        width: parseInt(process.env.DEFAULT_WIDTH) || 512,
        height: parseInt(process.env.DEFAULT_HEIGHT) || 512,
        numImages: 1,
        guidanceScale: 7.5,
        numInferenceSteps: 30,
        seed: -1
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    console.log('⚙️ Nastavenia:', finalOptions);
    
    // Príprava požiadavky
    const requestBody = {
        prompt: prompt,
        width: finalOptions.width,
        height: finalOptions.height,
        numImages: finalOptions.numImages,
        guidanceScale: finalOptions.guidanceScale,
        numInferenceSteps: finalOptions.numInferenceSteps,
        seed: finalOptions.seed
    };
    
    // Hlavičky požiadavky
    const headers = {
        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
        'Content-Type': 'application/json'
    };
    
    try {
        console.log('📡 Odosielam požiadavku na Scenario API...');
        
        // Odoslanie požiadavky
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody)
        });
        
        console.log('📊 Status kód:', response.status);
        console.log('📋 Status text:', response.statusText);
        
        // Spracovanie odpovede
        const responseData = await response.json();
        
        if (!response.ok) {
            console.error('❌ Chyba API:', responseData);
            return null;
        }
        
        console.log('✅ Úspešná odpoveď z API:');
        console.log(JSON.stringify(responseData, null, 2));
        
        // Ak obsahuje obrázky, stiahni ich
        if (responseData.images && responseData.images.length > 0) {
            await downloadImages(responseData.images, prompt);
        }
        
        return responseData;
        
    } catch (error) {
        console.error('💥 Chyba pri volaní API:', error.message);
        return null;
    }
}

// Funkcia pre stiahnutie obrázkov
async function downloadImages(images, prompt) {
    console.log(`📥 Sťahujem ${images.length} obrázok(ov)...`);
    
    // Vytvorenie výstupného priečinka
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
        console.log(`📁 Vytvorený priečinok: ${OUTPUT_DIR}`);
    }
    
    for (let i = 0; i < images.length; i++) {
        const image = images[i];
        const imageUrl = image.url;
        
        if (!imageUrl) {
            console.warn(`⚠️ Obrázok ${i + 1} nemá URL`);
            continue;
        }
        
        try {
            console.log(`🖼️ Sťahujem obrázok ${i + 1}: ${imageUrl}`);
            
            const imageResponse = await fetch(imageUrl);
            
            if (!imageResponse.ok) {
                console.error(`❌ Chyba pri sťahovaní obrázka ${i + 1}:`, imageResponse.statusText);
                continue;
            }
            
            const imageBuffer = await imageResponse.buffer();
            
            // Vytvorenie názvu súboru
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const sanitizedPrompt = prompt.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
            const filename = `${sanitizedPrompt}_${timestamp}_${i + 1}.png`;
            const filepath = path.join(OUTPUT_DIR, filename);
            
            // Uloženie súboru
            fs.writeFileSync(filepath, imageBuffer);
            console.log(`💾 Obrázok uložený: ${filepath}`);
            
        } catch (error) {
            console.error(`💥 Chyba pri sťahovaní obrázka ${i + 1}:`, error.message);
        }
    }
}

// Funkcia pre testovanie rôznych promptov
async function testMultiplePrompts() {
    const prompts = [
        'Gothic haunted castle at night',
        'Van Helsing vampire hunter with crossbow, Victorian era',
        'Dark fantasy forest with mysterious fog',
        'Ancient vampire crypt with stone coffins',
        'Gothic cathedral interior with stained glass'
    ];
    
    console.log('🧪 Testovanie viacerých promptov...\n');
    
    for (const prompt of prompts) {
        console.log(`\n${'='.repeat(60)}`);
        await generateImage(prompt);
        console.log(`${'='.repeat(60)}\n`);
        
        // Pauza medzi požiadavkami
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
}

// Hlavná funkcia
async function main() {
    console.log('🚀 Scenario API Image Generator');
    console.log('================================\n');
    
    // Kontrola API kľúča
    if (!API_KEY) {
        console.error('❌ SCENARIO_API_KEY nie je nastavený v .env súbore');
        process.exit(1);
    }
    
    console.log('🔑 API Key:', API_KEY.substring(0, 10) + '...');
    console.log('🌐 Base URL:', BASE_URL);
    console.log('📁 Output Dir:', OUTPUT_DIR);
    console.log('');
    
    // Získanie argumentov z príkazového riadku
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // Predvolený prompt
        const defaultPrompt = 'Gothic haunted castle at night';
        await generateImage(defaultPrompt);
    } else if (args[0] === '--test') {
        // Testovanie viacerých promptov
        await testMultiplePrompts();
    } else {
        // Vlastný prompt z argumentov
        const customPrompt = args.join(' ');
        await generateImage(customPrompt);
    }
}

// Spustenie skriptu
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error('💥 Kritická chyba:', error);
        process.exit(1);
    });
}

export { generateImage, downloadImages };
