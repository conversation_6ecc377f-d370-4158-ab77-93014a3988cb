# 🌪️ ATMOSFÉRICKÉ ZVUKY - IMPLEMENTÁCIA

## Prehľad
Implementovaný systém atmosf<PERSON><PERSON><PERSON><PERSON> zvukov pre kapitoly 1-3 s automatickými triggermi a plynulými prechodmi.

## 📁 Zvu<PERSON>é súbory
```
audio/effects/
├── storm.mp3          # Kapitola 1 - b<PERSON><PERSON>
├── brána.mp3          # Kapitola 2 - opustený zámok
└── Vstupná hala.mp3   # Kapitola 3 - vs<PERSON><PERSON><PERSON> hala
```

## 🎵 AudioManager - Nové funkcie

### Z<PERSON>ladn<PERSON> funkcie
- `play_atmosphere(sound_name, volume_percent, loop)` - Prehrá atmosférický zvuk
- `change_atmosphere_volume(volume_percent, fade_time)` - <PERSON><PERSON><PERSON> s fade
- `stop_atmosphere(fade_time)` - Zastav<PERSON> s fade out
- `crossfade_atmosphere(new_sound, volume_percent, fade_time)` - Crossfade medzi zvukmi

### Kapitola-špecifick<PERSON> funkcie

#### KAPITOLA 1 - STORM (zn<PERSON><PERSON><PERSON><PERSON> o 50%)
- `start_storm_atmosphere()` - <PERSON>pust<PERSON> na 50%
- `storm_narrator_start()` - [ROZPRÁVAČ_001] 35%
- `storm_chaos()` - [ROZPRÁVAČ_006] 42.5%
- `storm_forest()` - [ROZPRÁVAČ_015] 20%
- `storm_fade_out()` - [ROZPRÁVAČ_022] fade out

#### KAPITOLA 2 - BRÁNA
- `start_gate_atmosphere()` - [ROZPRÁVAČ_022] 60%
- `gate_ravens()` - [ROZPRÁVAČ_025] 70%
- `gate_puzzle_emphasis()` - [HLAVOLAM_3_UVOD] 75%
- `gate_opening()` - [ROZPRÁVAČ_026] 80%
- `gate_courtyard()` - [ROZPRÁVAČ_028] 50%

#### KAPITOLA 3 - VSTUPNÁ HALA
- `start_hall_atmosphere()` - [ROZPRÁVAČ_030] 70%
- `hall_clock_emphasis()` - [ROZPRÁVAČ_032] 80%
- `hall_library_transition()` - [ROZPRÁVAČ_033] 30%
- `hall_stop()` - [ROZPRÁVAČ_038] stop

## 🎯 Triggery v DialogueSystem

### Automatické spustenie
- **Kapitola 1**: Storm sa spustí automaticky pri štarte kapitoly
- **Kapitola 2**: Brána sa spustí pri príchode k bráne
- **Kapitola 3**: Hala sa spustí pri vstupe do haly

### Textové triggery
Systém kontroluje text rozprávača a automaticky spúšťa príslušné atmosférické efekty:

#### Kapitola 1 - Storm triggery
- "Búrka sa zúri" → spustí storm
- "V tejto temnej noci" → zníženie na 70%
- "živelný chaos" → zvýšenie na 85%
- "vstupujete do lesa" → zníženie na 40%
- "pred bránou" → fade out

#### Kapitola 2 - Brána triggery
- "pred bránou" → spustí bránu
- "havrany krákajú" → zvýšenie hlasitosti
- "brána sa otvára" → dramatický moment
- "nádvorie" → prechod na podklad

#### Kapitola 3 - Hala triggery
- "vstupná hala" → spustí halu
- "tikanie hodín" → zdôraznenie
- "knižnica" → zníženie na 30%
- "staré krídlo" → zastavenie

## 🔄 Prechody medzi zvukmi

### STORM → BRÁNA
- **Kedy**: Pri [ROZPRÁVAČ_022]
- **Ako**: 5s fade out storm + 3s fade in brána
- **Prekrytie**: Posledné 2 sekundy

### BRÁNA → VSTUPNÁ HALA
- **Kedy**: Pri [ROZPRÁVAČ_030]
- **Ako**: 3s crossfade
- **Prekrytie**: Plynulý prechod

## 🧪 Test scéna
Vytvorená test scéna `AtmosphereTestScene.tscn` pre testovanie všetkých atmosférických efektov:
- Prístup cez hlavné menu → "🌪️ Test Atmosféry"
- Tlačidlá pre všetky kapitoly a efekty
- Možnosť testovania prechodov a hlasitosti

## 📋 Implementované súbory

### Upravené súbory
- `scripts/AudioManager.gd` - Nové atmosférické funkcie
- `scripts/DialogueSystem.gd` - Textové triggery
- `scripts/Chapter.gd` - Auto-spustenie a puzzle triggery
- `scenes/MainMenu.tscn` - Test tlačidlo
- `scripts/MainMenu.gd` - Test funkcia

### Nové súbory
- `scenes/AtmosphereTestScene.tscn` - Test scéna
- `scripts/AtmosphereTestScene.gd` - Test script
- `docs/ATMOSFERICKE_ZVUKY.md` - Táto dokumentácia

## 🔧 Aktualizácie hlasitosti
- **Storm zvuky znížené o 50%**: 50% → 35% → 42.5% → 20% → fade out
- **Hint dialógy opravené**: Správne centrovanie a responzívne pozicovanie

## ✅ Výsledok
- **Automatické spustenie** atmosférických zvukov v kapitolách 1-3
- **Plynulé prechody** medzi zvukmi s crossfade efektmi
- **Responzívne triggery** na základe textu rozprávača
- **Testovacia scéna** pre overenie funkčnosti
- **Konzistentná hlasitosť** s možnosťou dynamických zmien
- **Správne centrované hint dialógy** bez pretekania cez obrazovku

Systém je plne funkčný a pripravený na použitie v hre!
