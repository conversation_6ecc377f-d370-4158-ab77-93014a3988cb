extends Control

# Nová sekcia "O hre" s krásnym Dark Templar dizajnom

@onready var main_panel: NinePatchRect = $MainPanel
@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var content_panel: NinePatchRect = $MainPanel/ContentContainer/ContentPanel
@onready var scroll_container: ScrollContainer = $MainPanel/ContentContainer/ContentPanel/ScrollContainer
@onready var game_title: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameTitleContainer/GameTitle
@onready var description: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/Description
@onready var privacy_policy_button: TextureButton = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/PrivacyPolicyButton
@onready var privacy_policy_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/PrivacyPolicyButton/PrivacyPolicyLabel
@onready var terms_of_service_button: TextureButton = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/TermsOfServiceButton
@onready var terms_of_service_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/TermsOfServiceButton/TermsOfServiceLabel
@onready var back_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/BackButton
@onready var back_label: Label = $MainPanel/ContentContainer/ButtonContainer/BackButton/BackLabel

func _ready():
	print("Nová AboutGame sekcia načítaná")

	# Pripojenie signálov
	setup_signals()

	# Aplikovanie fontov a štýlov
	apply_styling()

	# Nastavenie obsahu
	setup_content()

	# Nastavenie mobilného scrollovania
	setup_mobile_scrolling()

	# Nastavenie fokusu
	if back_button:
		back_button.grab_focus()

func setup_signals():
	"""Pripojí signály pre buttony"""
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
	if privacy_policy_button:
		privacy_policy_button.pressed.connect(_on_privacy_policy_pressed)
	if terms_of_service_button:
		terms_of_service_button.pressed.connect(_on_terms_of_service_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""

	if FontLoader:
		# Titulok sekcie
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 42)
			title_label.add_theme_constant_override("outline_size", 2)
			title_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

		# Titulok hry
		if game_title:
			FontLoader.apply_font_style(game_title, "chapter_title")
			game_title.add_theme_color_override("font_color", Color("#D4AF37"))
			game_title.add_theme_font_size_override("font_size", 32)
			game_title.add_theme_constant_override("outline_size", 2)
			game_title.add_theme_color_override("font_outline_color", Color("#2A1810"))

		# Popis hry
		if description:
			FontLoader.apply_font_style(description, "character_dialogue")
			description.add_theme_color_override("default_color", Color("#F5F5DC"))
			description.add_theme_font_size_override("normal_font_size", 20)
			description.add_theme_color_override("font_outline_color", Color("#2A1810"))
			description.add_theme_constant_override("outline_size", 1)

		# Tlačidlo späť
		if back_label:
			FontLoader.apply_font_style(back_label, "ui_elements")
			back_label.add_theme_color_override("font_color", Color("#D4AF37"))
			back_label.add_theme_font_size_override("font_size", 28)
			back_label.add_theme_constant_override("outline_size", 2)
			back_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

func setup_content():
	"""Nastaví obsah sekcie"""
	if title_label:
		title_label.text = "O HRE"
	
	if game_title:
		game_title.text = "VAN HELSING: PREKLIATE DEDIČSTVO"
	
	if description:
		description.text = """🕯️ [b]O hre[/b]

– „Prekliate dedičstvo je temná gotická audiohra, ktorá vás vtiahne do sveta zabudnutých tajomstiev a starých rodinných klenotov."

– „Staňte sa členom Rádu Striebornej ruže a odhaľte pravdu, ktorá bola po stáročia ukrytá v tieni karpatských hôr."

– „Čakajú vás hádanky, desivé odhalenia a príbeh, ktorý vás donúti spochybniť hranice medzi životom a smrťou."

[center][i]Verzia 1.0.0[/i][/center]"""
	
	if back_label:
		back_label.text = "SPÄŤ"

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _on_privacy_policy_pressed():
	"""Otvorí Privacy Policy obrazovku"""
	print("Otváram Privacy Policy")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/PrivacyPolicyScreen.tscn")

func _on_terms_of_service_pressed():
	"""Otvorí Terms of Service obrazovku"""
	print("Otváram Terms of Service")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/TermsOfServiceScreen.tscn")

func setup_mobile_scrolling():
	"""Nastaví optimálne scrollovanie pre mobilné zariadenia"""
	if scroll_container:
		# Povoliť vertikálne scrollovanie s automatickým scrollbarom
		scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
		scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO

		# Nastaviť deadzone pre lepšie dotyky
		scroll_container.scroll_deadzone = 0

		# Povoliť smooth scrolling
		scroll_container.follow_focus = true

		print("📱 Mobilné scrollovanie nastavené pre AboutGame")

func _input(event):
	"""Spracovanie klávesových skratiek a mobilných dotykových gestúr"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()

	# Podpora pre mobilné scrollovanie pomocou swipe gestúr
	if event is InputEventScreenDrag and scroll_container:
		var drag_speed = 2.0
		scroll_container.scroll_vertical -= int(event.relative.y * drag_speed)
