extends Control

# AudioSettings - Menu pre nastavenie hlasitosti audio s Dark Templar UI

@onready var music_slider = $MainPanel/VBoxContainer/MusicContainer/MusicSlider
@onready var sfx_slider = $MainPanel/VBoxContainer/SFXContainer/SFXSlider
@onready var ui_slider = $MainPanel/VBoxContainer/UIContainer/UISlider

@onready var music_value = $MainPanel/VBoxContainer/MusicContainer/MusicValue
@onready var sfx_value = $MainPanel/VBoxContainer/SFXContainer/SFXValue
@onready var ui_value = $MainPanel/VBoxContainer/UIContainer/UIValue

@onready var title_label = $MainPanel/VBoxContainer/TitleLabel
@onready var test_button = $MainPanel/VBoxContainer/ButtonContainer/TestButton
@onready var back_button = $MainPanel/VBoxContainer/ButtonContainer/BackButton

# Label references for styling
@onready var test_label = $MainPanel/VBoxContainer/ButtonContainer/TestButton/TestLabel
@onready var back_label = $MainPanel/VBoxContainer/ButtonContainer/BackButton/BackLabel

var test_track_index = 0
var test_tracks = ["main_menu", "storm_journey", "castle_gates", "puzzle_theme"]

func _ready():
	print("🎛️ AudioSettings menu načítané")

	# Kontrola dostupnosti nodov
	validate_node_references()

	# Aplikovanie gotických fontov
	apply_gothic_fonts()

	# Načítanie aktuálnych hodnôt
	load_current_values()
	
	# Pripojenie signálov
	connect_signals()

func validate_node_references():
	"""Kontrola dostupnosti všetkých potrebných nodov"""
	var missing_nodes = []

	if not music_slider:
		missing_nodes.append("music_slider")
	if not sfx_slider:
		missing_nodes.append("sfx_slider")
	if not ui_slider:
		missing_nodes.append("ui_slider")
	if not music_value:
		missing_nodes.append("music_value")
	if not sfx_value:
		missing_nodes.append("sfx_value")
	if not ui_value:
		missing_nodes.append("ui_value")
	if not test_button:
		missing_nodes.append("test_button")
	if not back_button:
		missing_nodes.append("back_button")

	if missing_nodes.size() > 0:
		print("⚠️ AudioSettings: Chýbajúce nody: ", missing_nodes)
	else:
		print("✅ AudioSettings: Všetky nody sú dostupné")

func apply_gothic_fonts():
	"""Aplikuje gotické fonty na UI elementy"""
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
		if test_label:
			FontLoader.apply_font_style(test_label, "ui_elements")
			# Nastav farbu textu na zlatú pre labely
			test_label.add_theme_color_override("font_color", Color("#D4AF37"))
			# Pridaj tieň pre lepšiu čitateľnosť
			test_label.add_theme_color_override("font_shadow_color", Color.BLACK)
			test_label.add_theme_constant_override("shadow_offset_x", 2)
			test_label.add_theme_constant_override("shadow_offset_y", 2)

		if back_label:
			FontLoader.apply_font_style(back_label, "ui_elements")
			back_label.add_theme_color_override("font_color", Color("#D4AF37"))
			back_label.add_theme_color_override("font_shadow_color", Color.BLACK)
			back_label.add_theme_constant_override("shadow_offset_x", 2)
			back_label.add_theme_constant_override("shadow_offset_y", 2)

func load_current_values():
	"""Načíta aktuálne audio nastavenia"""
	if music_slider:
		music_slider.value = AudioManager.get_music_volume()
	if sfx_slider:
		sfx_slider.value = AudioManager.get_sfx_volume()
	if ui_slider:
		ui_slider.value = AudioManager.get_ui_volume()

	update_value_labels()

func connect_signals():
	"""Pripojí signály pre slidery a tlačidlá"""
	music_slider.value_changed.connect(_on_music_volume_changed)
	sfx_slider.value_changed.connect(_on_sfx_volume_changed)
	ui_slider.value_changed.connect(_on_ui_volume_changed)
	
	test_button.pressed.connect(_on_test_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func update_value_labels():
	"""Aktualizuje percentuálne hodnoty"""
	music_value.text = str(int(music_slider.value * 100)) + "%"
	sfx_value.text = str(int(sfx_slider.value * 100)) + "%"
	ui_value.text = str(int(ui_slider.value * 100)) + "%"

func _on_music_volume_changed(value: float):
	"""Zmena hlasitosti hudby"""
	AudioManager.set_music_volume(value)
	update_value_labels()
	print("🎵 Hudba nastavená na: ", int(value * 100), "%")

func _on_sfx_volume_changed(value: float):
	"""Zmena hlasitosti SFX"""
	AudioManager.set_sfx_volume(value)
	update_value_labels()
	print("🔊 SFX nastavené na: ", int(value * 100), "%")

func _on_ui_volume_changed(value: float):
	"""Zmena hlasitosti UI"""
	AudioManager.set_ui_volume(value)
	update_value_labels()
	print("🔘 UI zvuky nastavené na: ", int(value * 100), "%")

func _on_test_button_pressed():
	"""Otvoriť test scénu pre všetky tracky"""
	print("🎵 Otváram test scénu")
	get_tree().change_scene_to_file("res://scenes/AudioTestScene.tscn")

func _on_back_button_pressed():
	"""Návrat do hlavného menu"""
	print("🔙 Návrat do hlavného menu")
	# Main menu hudba sa spustí automaticky v MainMenu.gd
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
