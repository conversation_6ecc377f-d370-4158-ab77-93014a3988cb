extends Control

# Test scéna pre FontVariation systém

func _ready():
	print("=== FONT VARIATION TEST SCÉNA ===")
	apply_fonts_to_labels()

func apply_fonts_to_labels():
	"""Aplikuje rôzne fonty na test labely"""
	
	# Získanie labelov
	var title_label = $VBoxContainer/TitleLabel
	var dialogue_label = $VBoxContainer/DialogueLabel
	var narrator_label = $VBoxContainer/NarratorLabel
	var ui_label = $VBoxContainer/UILabel
	var puzzle_label = $VBoxContainer/PuzzleLabel
	
	print("Aplikujem fonty na test labely...")
	
	# Aplikovanie fontov pomocou FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			print("✅ Chapter title font aplikovaný")

		if dialogue_label:
			FontLoader.apply_font_style(dialogue_label, "character_dialogue")
			print("✅ Character dialogue font aplikovaný")

		if narrator_label:
			FontLoader.apply_font_style(narrator_label, "narrator_text")
			print("✅ Narrator text font aplikovaný")

		if ui_label:
			FontLoader.apply_font_style(ui_label, "ui_elements")
			print("✅ UI elements font aplikovaný")

		if puzzle_label:
			FontLoader.apply_font_style(puzzle_label, "puzzle_text")
			print("✅ Puzzle text font aplikovaný")
	
	print("=== VŠETKY FONTY APLIKOVANÉ ===")
	
	# Test vlastností FontVariation
	test_font_properties()

func test_font_properties():
	"""Testuje vlastnosti FontVariation fontov"""
	print("\n--- TESTOVANIE FONT VLASTNOSTÍ ---")

	if FontLoader:
		# Test chapter title fontu (bold)
		var chapter_font = FontLoader.create_font_variation("chapter_title")
		if chapter_font:
			print("Chapter font - Embolden: ", chapter_font.variation_embolden)
			print("Chapter font - Base names: ", chapter_font.base_font.font_names)

		# Test narrator fontu (italic)
		var narrator_font = FontLoader.create_font_variation("narrator_text")
		if narrator_font:
			print("Narrator font - Transform: ", narrator_font.variation_transform)
			print("Narrator font - Base names: ", narrator_font.base_font.font_names)

		# Test UI fontu (medium)
		var ui_font = FontLoader.create_font_variation("ui_elements")
		if ui_font:
			print("UI font - Embolden: ", ui_font.variation_embolden)
			print("UI font - Base names: ", ui_font.base_font.font_names)

	print("--- TESTOVANIE DOKONČENÉ ---")
