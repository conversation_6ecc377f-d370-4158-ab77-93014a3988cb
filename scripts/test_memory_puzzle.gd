@tool
extends EditorScript

# Test skript pre overenie pamäťového hlavolamu
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Test pamäťového hlavolamu ===")
	
	# Skontroluj, či existujú potrebné súbory
	var files_to_check = [
		"res://scripts/MemoryTestPuzzle.gd",
		"res://scenes/MemoryTestPuzzle.tscn",
		"res://assets/Farby/Cervena_1.png",
		"res://assets/Farby/Modra_1.png",
		"res://assets/Farby/Zelena_1.png"
	]
	
	for file_path in files_to_check:
		if ResourceLoader.exists(file_path):
			print("✅ ", file_path, " - OK")
		else:
			print("❌ ", file_path, " - CHÝBA!")
	
	# Skontroluj, či sa scéna dá načítať
	var scene_resource = load("res://scenes/MemoryTestPuzzle.tscn")
	if scene_resource:
		print("✅ MemoryTestPuzzle.tscn sa načítala úspešne")
		
		# Skús vytvoriť inštanciu
		var instance = scene_resource.instantiate()
		if instance:
			print("✅ Inštancia sa vytvorila úspešne")
			print("✅ Typ inštancie: ", instance.get_class())
			
			# Skontroluj, či má potrebné uzly
			var nodes_to_check = [
				"PuzzlePanel/VBoxContainer/ColorContainer/RedButton",
				"PuzzlePanel/VBoxContainer/ColorContainer/BlueButton", 
				"PuzzlePanel/VBoxContainer/ColorContainer/GreenButton"
			]
			
			for node_path in nodes_to_check:
				var node = instance.get_node_or_null(node_path)
				if node:
					print("✅ Uzol ", node_path, " - OK (typ: ", node.get_class(), ")")
				else:
					print("❌ Uzol ", node_path, " - CHÝBA!")
			
			instance.queue_free()
		else:
			print("❌ Nepodarilo sa vytvoriť inštanciu")
	else:
		print("❌ MemoryTestPuzzle.tscn sa nepodarilo načítať")
	
	print("=== Test dokončený ===")
