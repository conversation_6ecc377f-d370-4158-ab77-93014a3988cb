extends Control

# Terms of Service obrazovka

@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var terms_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/TermsText
@onready var back_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/BackButton
@onready var back_label: Label = $MainPanel/ContentContainer/ButtonContainer/BackButton/BackLabel

func _ready():
	print("Terms of Service obrazovka načítaná")
	
	# Pripojenie signálov
	setup_signals()
	
	# Aplikovanie fontov a štýlov
	apply_styling()
	
	# Nastavenie obsahu
	setup_content()
	
	# Nastavenie fokusu
	if back_button:
		back_button.grab_focus()

func setup_signals():
	"""Pripojí signály pre buttony"""
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""
	# Aplikovanie fontov cez FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 28)
		
		if terms_text:
			FontLoader.apply_font_style(terms_text, "dialogue")
			terms_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			terms_text.add_theme_font_size_override("normal_font_size", 16)
		
		if back_label:
			FontLoader.apply_font_style(back_label, "ui")
			back_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			back_label.add_theme_font_size_override("font_size", 18)

func setup_content():
	"""Nastaví obsah Terms of Service"""
	if title_label:
		title_label.text = "PODMIENKY POUŽÍVANIA"
	
	if terms_text:
		terms_text.text = """[center][b]Podmienky používania (Terms of Service)[/b][/center]

Používaním hry Van Helsing: Prekliate dedičstvo súhlasíte s týmito podmienkami:

• Hru môžete používať len na osobné, nekomerčné účely.

• Zakazuje sa upravovať, kopírovať alebo spätne analyzovať (reverzné inžinierstvo) túto hru bez písomného súhlasu tvorcu.

• Tvorca hry nezodpovedá za žiadne škody, ktoré by mohli vzniknúť v súvislosti s používaním tejto hry.

• Tieto podmienky a všetky spory sa riadia zákonmi Slovenskej republiky.

[center][b]Kontakt[/b][/center]

Ak máte akékoľvek otázky ohľadom podmienok používania, kontaktujte nás na:
• Telefón: 0940 400 222
• Email: <EMAIL>"""
	
	if back_label:
		back_label.text = "SPÄŤ"

func _on_back_pressed():
	"""Návrat do sekcie O hre"""
	print("Návrat do sekcie O hre")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/AboutGameNew.tscn")

func _input(event):
	"""Spracovanie klávesových skratiek"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
