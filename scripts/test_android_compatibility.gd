@tool
extends EditorScript

# Test skript pre overenie Android/iOS kompatibility
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== TEST ANDROID/iOS KOMPATIBILITY ===")
	print("Platform: ", OS.get_name())
	
	# Test 1: Overenie existencie kľúčových scén
	test_scene_existence()
	
	# Test 2: Overenie GameManager funkcií
	test_game_manager()
	
	# Test 3: Overenie AudioManager
	test_audio_manager()
	
	# Test 4: Overenie kapitol scén
	test_chapter_scenes()
	
	print("\n=== TEST DOKONČENÝ ===")

func test_scene_existence():
	print("\n📋 Test existencie kľúčových scén:")
	
	var critical_scenes = [
		"res://scenes/LoadingScreen.tscn",
		"res://scenes/SplashScreen.tscn", 
		"res://scenes/MainMenu.tscn",
		"res://scenes/NewChaptersMenu.tscn",
		"res://scenes/ChapterIntro.tscn"
	]
	
	var missing_count = 0
	for scene_path in critical_scenes:
		if ResourceLoader.exists(scene_path):
			print("✅ ", scene_path.get_file())
		else:
			print("❌ CHÝBA: ", scene_path.get_file())
			missing_count += 1
	
	if missing_count == 0:
		print("🎉 Všetky kľúčové scény existujú!")
	else:
		print("⚠️ Chýba ", missing_count, " kľúčových scén")

func test_game_manager():
	print("\n🎮 Test GameManager:")
	
	# Simulácia GameManager autoload
	var gm_path = "res://autoload/GameManager.gd"
	if ResourceLoader.exists(gm_path):
		print("✅ GameManager.gd existuje")
		
		# Test chapter info
		var script = load(gm_path)
		if script:
			print("✅ GameManager skript sa načítal")
		else:
			print("❌ GameManager skript sa nenačítal")
	else:
		print("❌ GameManager.gd neexistuje")

func test_audio_manager():
	print("\n🎵 Test AudioManager:")
	
	var am_path = "res://scripts/AudioManager.gd"
	if ResourceLoader.exists(am_path):
		print("✅ AudioManager.gd existuje")
	else:
		print("❌ AudioManager.gd neexistuje")

func test_chapter_scenes():
	print("\n📚 Test kapitol scén:")
	
	var missing_chapters = []
	for i in range(1, 8):  # Kapitoly 1-7
		var chapter_scene = "res://scenes/Chapter" + str(i) + ".tscn"
		if ResourceLoader.exists(chapter_scene):
			print("✅ Kapitola ", i, " existuje")
		else:
			print("❌ CHÝBA: Kapitola ", i)
			missing_chapters.append(i)
	
	if missing_chapters.size() == 0:
		print("🎉 Všetky kapitoly existujú!")
	else:
		print("⚠️ Chýbajú kapitoly: ", missing_chapters)
