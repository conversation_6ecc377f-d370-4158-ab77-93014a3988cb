[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bs1rxitbvl55j"
path="res://.godot/imported/CharacterCreate_Gender_Male.png-87e1a9ad425819821f2c9c8e13c8de99.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assets/craftpix-net-699601-rpg-mmo-ui/Textures/Lobby/Character Create/Genders/CharacterCreate_Gender_Male.png"
dest_files=["res://.godot/imported/CharacterCreate_Gender_Male.png-87e1a9ad425819821f2c9c8e13c8de99.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
