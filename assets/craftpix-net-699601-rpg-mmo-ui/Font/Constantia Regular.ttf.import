[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://my5w6r0b6l8u"
path="res://.godot/imported/Constantia Regular.ttf-e656ea6308dda52b1f39f41c4dc17cc8.fontdata"

[deps]

source_file="res://assets/craftpix-net-699601-rpg-mmo-ui/Font/Constantia Regular.ttf"
dest_files=["res://.godot/imported/Constantia Regular.ttf-e656ea6308dda52b1f39f41c4dc17cc8.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
