[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://c3m5p782g6x4i"
path="res://.godot/imported/frame_0057.png-41ebb5c4347fba44576995370a6e6352.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assets/chapter_sprites/chapter_1/frame_0057.png"
dest_files=["res://.godot/imported/frame_0057.png-41ebb5c4347fba44576995370a6e6352.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
